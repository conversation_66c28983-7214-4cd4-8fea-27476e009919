@extends('admin.dashboard')

@section('content')
<div class="main-content">
    <div class="page-content">
        <div class="container-fluid">
            <!-- start page title -->
            <div class="row">
                <div class="col-12">
                    <div class="page-title-box d-flex align-items-center justify-content-between">
                        <h4 class="mb-0">All Courses</h4>
                        <div class="page-title-right">
                            <ol class="breadcrumb m-0">
                                <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                                <li class="breadcrumb-item active">Courses</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
            <!-- end page title -->

            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <div>
                                    <h4 class="card-title">Courses Management</h4>
                                    <p class="card-title-desc">Manage all courses from here.</p>
                                </div>
                                <div>
                                    <a href="{{ route('admin.courses.create') }}" class="btn btn-primary">
                                        <i class="uil-plus me-1"></i> Add New Course
                                    </a>
                                </div>
                            </div>

                            @if (session('success'))
                                <div class="alert alert-success alert-dismissible fade show" role="alert">
                                    {{ session('success') }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            @endif

                            @if (session('error'))
                                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                    {{ session('error') }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            @endif

                            <!-- Search and Filter -->
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <div class="search-box">
                                        <div class="position-relative">
                                            <input type="text" class="form-control" id="searchInput" placeholder="Search courses...">
                                            <i class="uil-search search-icon"></i>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <select class="form-select" id="categoryFilter">
                                        <option value="">All Categories</option>
                                        @foreach($categories ?? [] as $category)
                                            <option value="{{ $category->id }}"
                                                    data-color="{{ $category->color ?? '#6777ef' }}"
                                                    data-icon="{{ $category->icon ?? '' }}">
                                                {{ $category->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <select class="form-select" id="statusFilter">
                                        <option value="">All Status</option>
                                        <option value="draft">Draft</option>
                                        <option value="published">Published</option>
                                        <option value="archived">Archived</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <select class="form-select" id="typeFilter">
                                        <option value="">All Types</option>
                                        <option value="free">Free</option>
                                        <option value="paid">Paid</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <select class="form-select" id="featuredFilter">
                                        <option value="">All Courses</option>
                                        <option value="1">Featured Only</option>
                                        <option value="0">Non-Featured</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Course Stats -->
                            <div class="row mb-4">
                                <div class="col-md-3">
                                    <div class="card border-0 bg-primary text-white">
                                        <div class="card-body">
                                            <div class="d-flex align-items-center">
                                                <div class="flex-shrink-0">
                                                    <i class="uil-book-open display-6"></i>
                                                </div>
                                                <div class="flex-grow-1 ms-3">
                                                    <h5 class="mb-0">{{ $totalCourses ?? 0 }}</h5>
                                                    <p class="mb-0">Total Courses</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card border-0 bg-success text-white">
                                        <div class="card-body">
                                            <div class="d-flex align-items-center">
                                                <div class="flex-shrink-0">
                                                    <i class="uil-check-circle display-6"></i>
                                                </div>
                                                <div class="flex-grow-1 ms-3">
                                                    <h5 class="mb-0">{{ $publishedCourses ?? 0 }}</h5>
                                                    <p class="mb-0">Published</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card border-0 bg-warning text-white">
                                        <div class="card-body">
                                            <div class="d-flex align-items-center">
                                                <div class="flex-shrink-0">
                                                    <i class="uil-edit display-6"></i>
                                                </div>
                                                <div class="flex-grow-1 ms-3">
                                                    <h5 class="mb-0">{{ $draftCourses ?? 0 }}</h5>
                                                    <p class="mb-0">Drafts</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card border-0 bg-info text-white">
                                        <div class="card-body">
                                            <div class="d-flex align-items-center">
                                                <div class="flex-shrink-0">
                                                    <i class="uil-star display-6"></i>
                                                </div>
                                                <div class="flex-grow-1 ms-3">
                                                    <h5 class="mb-0">{{ $featuredCourses ?? 0 }}</h5>
                                                    <p class="mb-0">Featured</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Category Overview -->
                            @if($categories && $categories->count() > 0)
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h6 class="mb-3">Courses by Category</h6>
                                    <div class="row">
                                        @foreach($categories->take(6) as $category)
                                            <div class="col-md-2 col-sm-4 col-6 mb-3">
                                                <div class="card border-0 h-100 category-card"
                                                     style="background: linear-gradient(135deg, {{ $category->color ?? '#6777ef' }}15, {{ $category->color ?? '#6777ef' }}05);"
                                                     onclick="filterByCategory({{ $category->id }})">
                                                    <div class="card-body text-center p-3">
                                                        @if($category->icon)
                                                            <i class="{{ $category->icon }} mb-2"
                                                               style="color: {{ $category->color ?? '#6777ef' }}; font-size: 24px;"></i>
                                                        @endif
                                                        <h6 class="mb-1 small">{{ $category->name }}</h6>
                                                        <span class="badge"
                                                              style="background-color: {{ $category->color ?? '#6777ef' }}; color: white; font-size: 10px;">
                                                            {{ $category->courses_count ?? 0 }} courses
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                            @endif

                            <div class="table-responsive">
                                <table class="table table-nowrap align-middle table-hover" id="coursesTable">
                                    <thead class="table-light">
                                        <tr>
                                            <th style="width: 50px;">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="selectAll">
                                                </div>
                                            </th>
                                            <th>Course</th>
                                            <th>Category</th>
                                            <th>Instructor</th>
                                            <th>Price</th>
                                            <th>Students</th>
                                            <th>Status</th>
                                            <th>Created</th>
                                            <th style="width: 120px;">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {{-- Sample data - replace with actual data from controller --}}
                                        @forelse($courses ?? [] as $course)
                                        <tr>
                                            <td>
                                                <div class="form-check">
                                                    <input class="form-check-input course-checkbox" type="checkbox" value="{{ $course->id }}">
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="flex-shrink-0 me-3">
                                                        @if($course->thumbnail)
                                                            <img src="{{ asset('storage/' . $course->thumbnail) }}" alt="{{ $course->title }}"
                                                                 class="rounded" style="width: 60px; height: 45px; object-fit: cover;">
                                                        @else
                                                            <div class="bg-light rounded d-flex align-items-center justify-content-center"
                                                                 style="width: 60px; height: 45px;">
                                                                <i class="uil-play text-muted"></i>
                                                            </div>
                                                        @endif
                                                    </div>
                                                    <div class="flex-grow-1">
                                                        <h6 class="mb-1">{{ $course->title }}</h6>
                                                        <p class="text-muted mb-0 small">{{ Str::limit($course->short_description, 60) }}</p>
                                                        <div class="mt-1">
                                                            @if($course->is_featured)
                                                                <span class="badge bg-warning me-1"><i class="uil-star"></i> Featured</span>
                                                            @endif
                                                            @if($course->is_free)
                                                                <span class="badge bg-success me-1">Free</span>
                                                            @endif
                                                            @if($course->duration)
                                                                <span class="badge bg-info me-1"><i class="uil-clock"></i> {{ $course->duration }}h</span>
                                                            @endif
                                                            @if($course->difficulty_level)
                                                                <span class="badge bg-secondary me-1">{{ ucfirst($course->difficulty_level) }}</span>
                                                            @endif
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                @if($course->category)
                                                    <div class="d-flex align-items-center">
                                                        @if($course->category->icon)
                                                            <i class="{{ $course->category->icon }} me-2"
                                                               style="color: {{ $course->category->color ?? '#6777ef' }}; font-size: 16px;"></i>
                                                        @endif
                                                        <span class="badge category-badge"
                                                              style="background-color: {{ $course->category->color ?? '#6777ef' }}; color: white;">
                                                            {{ $course->category->name }}
                                                        </span>
                                                    </div>
                                                @else
                                                    <span class="badge bg-secondary">Uncategorized</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="flex-shrink-0 me-2">
                                                        <img src="{{ $course->instructor->avatar ?? asset('assets/images/users/avatar-default.jpg') }}"
                                                             alt="{{ $course->instructor->name ?? 'Unknown' }}"
                                                             class="rounded-circle" style="width: 32px; height: 32px;">
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-0 small">{{ $course->instructor->name ?? 'Unknown' }}</h6>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                @if($course->is_free)
                                                    <span class="text-success fw-bold">Free</span>
                                                @else
                                                    <div>
                                                        @if($course->discount_price && $course->discount_price < $course->price)
                                                            <span class="text-decoration-line-through text-muted small">${{ number_format($course->price, 2) }}</span><br>
                                                            <span class="text-success fw-bold">${{ number_format($course->discount_price, 2) }}</span>
                                                        @else
                                                            <span class="fw-bold">${{ number_format($course->price, 2) }}</span>
                                                        @endif
                                                    </div>
                                                @endif
                                            </td>
                                            <td>
                                                <span class="badge bg-info">{{ $course->enrollments_count ?? 0 }} students</span>
                                            </td>
                                            <td>
                                                @switch($course->status)
                                                    @case('published')
                                                        <span class="badge bg-success">Published</span>
                                                        @break
                                                    @case('draft')
                                                        <span class="badge bg-warning">Draft</span>
                                                        @break
                                                    @case('archived')
                                                        <span class="badge bg-danger">Archived</span>
                                                        @break
                                                    @default
                                                        <span class="badge bg-secondary">Unknown</span>
                                                @endswitch
                                            </td>
                                            <td>{{ $course->created_at ? $course->created_at->format('M d, Y') : '-' }}</td>
                                            <td>
                                                <div class="dropdown">
                                                    <button class="btn btn-light btn-sm dropdown-toggle" type="button"
                                                            data-bs-toggle="dropdown" aria-expanded="false">
                                                        Actions
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li>
                                                            <a class="dropdown-item" href="{{ route('admin.courses.show', $course->id) }}">
                                                                <i class="uil-eye me-2"></i>View
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="dropdown-item" href="{{ route('admin.courses.edit', $course->id) }}">
                                                                <i class="uil-edit me-2"></i>Edit
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="dropdown-item" href="#">
                                                                <i class="uil-copy me-2"></i>Duplicate
                                                            </a>
                                                        </li>
                                                        <li><hr class="dropdown-divider"></li>
                                                        <li>
                                                            <a class="dropdown-item text-danger" href="#"
                                                               onclick="deleteCourse({{ $course->id }})">
                                                                <i class="uil-trash me-2"></i>Delete
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                        @empty
                                        <tr>
                                            <td colspan="9" class="text-center py-4">
                                                <div class="d-flex flex-column align-items-center">
                                                    <i class="uil-book-open display-4 text-muted mb-3"></i>
                                                    <h5 class="text-muted">No courses found</h5>
                                                    <p class="text-muted">Start by creating your first course</p>
                                                    <a href="{{ route('admin.courses.create') }}" class="btn btn-primary">
                                                        <i class="uil-plus me-1"></i> Add Course
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>

                            {{-- Pagination --}}
                            @if(isset($courses) && method_exists($courses, 'hasPages') && $courses->hasPages())
                                <div class="d-flex justify-content-between align-items-center mt-4">
                                    <div>
                                        <p class="text-muted">
                                            Showing {{ $courses->firstItem() }} to {{ $courses->lastItem() }}
                                            of {{ $courses->total() }} results
                                        </p>
                                    </div>
                                    <div>
                                        {{ $courses->links() }}
                                    </div>
                                </div>
                            @endif

                            {{-- Bulk Actions --}}
                            <div class="row mt-3" id="bulkActions" style="display: none;">
                                <div class="col-12">
                                    <div class="alert alert-info">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span><span id="selectedCount">0</span> courses selected</span>
                                            <div>
                                                <button class="btn btn-sm btn-success me-2" onclick="bulkAction('publish')">
                                                    <i class="uil-check me-1"></i>Publish
                                                </button>
                                                <button class="btn btn-sm btn-warning me-2" onclick="bulkAction('draft')">
                                                    <i class="uil-edit me-1"></i>Draft
                                                </button>
                                                <button class="btn btn-sm btn-danger" onclick="bulkAction('delete')">
                                                    <i class="uil-trash me-1"></i>Delete
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer class="footer">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-6">
                    <script>document.write(new Date().getFullYear())</script> © Lernovate.
                </div>
                <div class="col-sm-6">
                    <div class="text-sm-end d-none d-sm-block">
                        Crafted with <i class="mdi mdi-heart text-danger"></i> by Lernovate Team
                    </div>
                </div>
            </div>
        </div>
    </footer>
</div>

{{-- Delete Confirmation Modal --}}
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this course? This action cannot be undone and will remove all associated data.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<style>
    .category-card {
        transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        cursor: pointer;
    }

    .category-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }

    .category-badge {
        transition: all 0.2s ease-in-out;
    }

    .category-badge:hover {
        transform: scale(1.05);
    }

    .search-box .search-icon {
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: #adb5bd;
    }
</style>

<script>
    // Delete course function
    function deleteCourse(courseId) {
        const deleteForm = document.getElementById('deleteForm');
        deleteForm.action = `/admin/courses-delete/${courseId}`;

        const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
        deleteModal.show();
    }

    // Filter by category function
    function filterByCategory(categoryId) {
        const categoryFilter = document.getElementById('categoryFilter');
        categoryFilter.value = categoryId;
        filterTable();

        // Scroll to table
        document.getElementById('coursesTable').scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }

    // Search functionality
    document.getElementById('searchInput').addEventListener('input', function() {
        filterTable();
    });

    // Filter functionality
    document.getElementById('categoryFilter').addEventListener('change', function() {
        filterTable();
    });

    document.getElementById('statusFilter').addEventListener('change', function() {
        filterTable();
    });

    document.getElementById('typeFilter').addEventListener('change', function() {
        filterTable();
    });

    document.getElementById('featuredFilter').addEventListener('change', function() {
        filterTable();
    });

    function filterTable() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const categoryFilter = document.getElementById('categoryFilter').value;
        const statusFilter = document.getElementById('statusFilter').value;
        const typeFilter = document.getElementById('typeFilter').value;
        const featuredFilter = document.getElementById('featuredFilter').value;
        const table = document.getElementById('coursesTable');
        const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');

        for (let i = 0; i < rows.length; i++) {
            const row = rows[i];
            if (row.cells.length < 9) continue; // Skip empty state row

            const courseTitle = row.cells[1].textContent.toLowerCase();
            const category = row.cells[2].textContent.toLowerCase();
            const instructor = row.cells[3].textContent.toLowerCase();
            const price = row.cells[4].textContent.toLowerCase();
            const status = row.cells[6].textContent.toLowerCase();
            const featured = row.cells[1].textContent.includes('Featured');
            const isFree = price.includes('free');

            let showRow = true;

            // Search filter
            if (searchTerm && !courseTitle.includes(searchTerm) && !instructor.includes(searchTerm)) {
                showRow = false;
            }

            // Category filter
            if (categoryFilter && !category.includes(categoryFilter.toLowerCase())) {
                showRow = false;
            }

            // Status filter
            if (statusFilter && !status.includes(statusFilter)) {
                showRow = false;
            }

            // Type filter (free/paid)
            if (typeFilter === 'free' && !isFree) {
                showRow = false;
            } else if (typeFilter === 'paid' && isFree) {
                showRow = false;
            }

            // Featured filter
            if (featuredFilter !== '') {
                if (featuredFilter === '1' && !featured) {
                    showRow = false;
                } else if (featuredFilter === '0' && featured) {
                    showRow = false;
                }
            }

            row.style.display = showRow ? '' : 'none';
        }
    }

    // Select all functionality
    document.getElementById('selectAll').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.course-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkActions();
    });

    // Individual checkbox functionality
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('course-checkbox')) {
            updateBulkActions();
        }
    });

    function updateBulkActions() {
        const checkboxes = document.querySelectorAll('.course-checkbox:checked');
        const bulkActions = document.getElementById('bulkActions');
        const selectedCount = document.getElementById('selectedCount');

        if (checkboxes.length > 0) {
            bulkActions.style.display = 'block';
            selectedCount.textContent = checkboxes.length;
        } else {
            bulkActions.style.display = 'none';
        }
    }

    // Bulk actions
    function bulkAction(action) {
        const checkboxes = document.querySelectorAll('.course-checkbox:checked');
        const courseIds = Array.from(checkboxes).map(cb => cb.value);

        if (courseIds.length === 0) {
            alert('Please select at least one course.');
            return;
        }

        let confirmMessage = '';
        switch(action) {
            case 'publish':
                confirmMessage = `Are you sure you want to publish ${courseIds.length} courses?`;
                break;
            case 'draft':
                confirmMessage = `Are you sure you want to move ${courseIds.length} courses to draft?`;
                break;
            case 'delete':
                confirmMessage = `Are you sure you want to delete ${courseIds.length} courses? This action cannot be undone.`;
                break;
        }

        if (confirm(confirmMessage)) {
            // Create form and submit
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/admin/courses-bulk-action';

            // CSRF token
            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = '{{ csrf_token() }}';
            form.appendChild(csrfToken);

            // Action
            const actionInput = document.createElement('input');
            actionInput.type = 'hidden';
            actionInput.name = 'action';
            actionInput.value = action;
            form.appendChild(actionInput);

            // Course IDs
            courseIds.forEach(id => {
                const idInput = document.createElement('input');
                idInput.type = 'hidden';
                idInput.name = 'course_ids[]';
                idInput.value = id;
                form.appendChild(idInput);
            });

            document.body.appendChild(form);
            form.submit();
        }
    }
</script>
@endsection
